<template>
  <div class="min-h-screen bg-white dark:bg-zinc-800 flex items-center justify-center px-4">
    <div class="max-w-lg mx-auto text-center">

      <div class="mb-8">
        <img
          src="/icon.png"
          alt="EventaHub Logo"
          class="h-16 w-16 mx-auto rounded-full shadow-lg"
        >
      </div>


      <div class="mb-8">
        <div class="text-9xl font-bold text-yellow-600 dark:text-yellow-500 mb-4">
          503
        </div>
        <div class="w-32 h-32 mx-auto mb-6">
          <svg class="w-full h-full text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>


      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Service Unavailable
        </h1>
        <p class="text-gray-600 dark:text-gray-400 text-lg mb-6">
          EventaHub is temporarily unavailable due to scheduled maintenance. We're working to improve your experience and will be back online shortly.
        </p>
      </div>

      <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-6 mb-8">
        <div class="flex items-center justify-center mb-4">
          <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span class="text-yellow-800 dark:text-yellow-200 font-medium">Scheduled Maintenance</span>
        </div>
        <p class="text-yellow-700 dark:text-yellow-300 text-sm">
          Expected completion: {{ estimatedCompletion }}
        </p>
      </div>

      <div class="bg-gray-50 dark:bg-gray-700 p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          What's happening:
        </h3>
        <ul class="text-left text-gray-600 dark:text-gray-400 space-y-2">
          <li class="flex items-center">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
            </svg>
            System improvements and updates
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Enhanced security measures
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Performance optimizations
          </li>
        </ul>
      </div>

      <div class="space-y-4">
        <button
          @click="checkStatus"
          class="inline-block bg-yellow-600 hover:bg-yellow-700 text-white font-semibold py-3 px-8 transition-all duration-200 transform hover:scale-105 shadow-lg"
        >
          Check Status
        </button

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="https://twitter.com/EventaHubMW"
            target="_blank"
            rel="noopener noreferrer"
            class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 transition-all duration-200 text-sm shadow-md"
          >
            Follow Updates
          </a>
          <NuxtLink
            to="/contact-us"
            class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-6 transition-all duration-200 text-sm shadow-md"
          >
            Contact Support
          </NuxtLink>
        </div>
      </div>


      <div class="mt-8 text-sm text-gray-500 dark:text-gray-400">
        <p>This page will automatically refresh every {{ refreshInterval / 1000 }} seconds.</p>
        <p class="mt-1">Last checked: {{ lastChecked }}</p>
      </div>


      <div class="mt-12 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} EventaHub Malawi. All rights reserved.</p>
        <p class="mt-1">Powered by Brainy Technologies</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Reactive data
const refreshInterval = 30000 // 30 seconds
const lastChecked = ref(new Date().toLocaleTimeString())
const estimatedCompletion = ref('Within 2 hours')

// Auto-refresh functionality
let refreshTimer: NodeJS.Timeout

onMounted(() => {
  // Set up auto-refresh
  refreshTimer = setInterval(() => {
    lastChecked.value = new Date().toLocaleTimeString()
    // In a real scenario, you might want to check server status here
    window.location.reload()
  }, refreshInterval)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

const checkStatus = () => {
  lastChecked.value = new Date().toLocaleTimeString()
  window.location.reload()
}

// Set page meta
useHead({
  title: '503 - Service Unavailable | EventaHub',
  meta: [
    { name: 'description', content: 'EventaHub is temporarily unavailable due to maintenance. We will be back online shortly.' },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})

// Set layout to false for full control
definePageMeta({
  layout: false
})
</script>
